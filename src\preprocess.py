import pandas as pd, sys, yaml, os

params = yaml.safe_load(open("params.yaml"))['preprocess']

def preprocess(input_path, output_path):
    df = pd.read_csv(input_path)

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    df.to_csv(output_path, header=None, index=False)

    print(f"Preprocessing data saved at {output_path}")

if __name__ == "__main__":
    preprocess(params['input'], params['output'])
    